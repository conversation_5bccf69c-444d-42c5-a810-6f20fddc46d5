package com.ybm100.app.crm.global;

import android.app.Application;
import android.text.TextUtils;

import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.platform.RuntimeEnv;
import com.ybm100.app.crm.config.AppNetConfig;

import io.flutter.app.FlutterApplication;

/**
 * Quick Tracking SDK预初始化Application
 * 用于满足隐私政策合规要求
 */
public class App extends FlutterApplication {

    @Override
    public void onCreate() {
        super.onCreate();
        
        // Quick Tracking SDK预初始化
        initQuickTrackingPreInit();
    }

    /**
     * Quick Tracking SDK预初始化
     * 注意：这里的appkey和渠道参数必须和后续在flutter工程的dart代码中调用的
     * QTCommonSdk.initCommon传入的参数严格一致
     */
    private void initQuickTrackingPreInit() {
        try {
            // 动态获取当前环境的appkey
            String appKey = getQuickTrackingAppKey();
            String channel = getQuickTrackingChannel();
            
            if (!TextUtils.isEmpty(appKey)) {
                // 调用预初始化方法
                Class<?> qtConfigureClass = Class.forName("com.quick.qt.analytics.QtConfigure");
                java.lang.reflect.Method preInitMethod = qtConfigureClass.getMethod("preInit", 
                    android.content.Context.class, String.class, String.class);
                preInitMethod.invoke(null, this, appKey, channel);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取Quick Tracking AppKey
     * 根据当前环境返回对应的AppKey
     */
    private String getQuickTrackingAppKey() {
        if (isAppProd()) {
            // 生产环境AppKey - 需要替换为实际的生产环境AppKey
            return "your_production_appkey_here";
        } else {
            // 测试环境AppKey - 需要替换为实际的测试环境AppKey
            return "your_test_appkey_here";
        }
    }

    /**
     * 获取Quick Tracking渠道
     */
    private String getQuickTrackingChannel() {
        if (isAppProd()) {
            return "official"; // 生产环境渠道
        } else {
            return "test"; // 测试环境渠道
        }
    }

    /**
     * 判断是否为生产环境
     */
    private boolean isAppProd() {
        String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
        if (TextUtils.isEmpty(currFlavor) || AppNetConfig.FlavorType.CUSTOM.equals(currFlavor)) {
            currFlavor = RuntimeEnv.INSTANCE.getEnv();
        }
        return AppNetConfig.FlavorType.PROD.equalsIgnoreCase(currFlavor);
    }
}
