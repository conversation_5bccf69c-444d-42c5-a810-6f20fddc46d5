apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply from: 'tinker-support.gradle'
apply plugin: 'kotlin-kapt'
android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        applicationId rootProject.ext.android.applicationId
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode project.hasProperty("versionCode") ? Integer.parseInt(project.versionCode) : rootProject.ext.android.versionCode
        versionName project.hasProperty("versionName") ? project.versionName : rootProject.ext.android.versionName
//        manifestPlaceholders = [UMENG_CHANNEL_VALUE: "default"]

        // 支持多dex
        multiDexEnabled true
        multiDexKeepProguard file("multiDexKeep.pro") //分包时，将部分包放入主dex


        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName(), AROUTER_GENERATE_DOC: "enable"]
            }
        }

        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }

        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
        buildConfigField("String", "API_URL_PROD", rootProject.ext.apiUrl.releaseUrl)
    }

    signingConfigs {
        //开发者签名
        production {
            storeFile file("keystore/ybm.jks")
            storePassword 'ybmmarket20'
            keyAlias '小药药'
            keyPassword 'ybmmarket20'
        }
    }

    buildTypes {
        debug {
            //不混淆
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            signingConfig signingConfigs.production
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters 'armeabi', 'x86', 'armeabi-v7a'//x86用于兼容模拟器
            }
        }

        release {
            //混淆
            minifyEnabled true
            //Zipalign优化
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources true
            signingConfig signingConfigs.production
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters 'armeabi-v7a','arm64-v8a' //理论上armeabi-v7a就够了，这里保留下armeabi防止老机型兼容问题
            }

            //批量修改Apk名字
            applicationVariants.all { variant ->
                variant.outputs.all { output ->
                    outputFileName = "xyy-bean-sprouts-${variant.name}-${defaultConfig.versionName}-${defaultConfig.versionCode}.apk"
                }
            }
        }
    }
    lintOptions {
        abortOnError false
        checkReleaseBuilds false//Could not download groovy-all.jar
        disable 'MissingTranslation'
    }
    flavorDimensions "default"
    productFlavors {
        beta {//测试
            dimension "default"
            rootProject.ext.android.pkgName = rootProject.ext.android.applicationTestId
            applicationId rootProject.ext.android.applicationTestId
            buildConfigField("String", "API_URL", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_TEST", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_DEV", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_STAGE", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "H5_URL", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_TEST", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_STAGE", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "H5_DOMAIN", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_TEST", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_STAGE", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "CDN_URL", rootProject.ext.apiUrl.testCDN)
            buildConfigField("String", "STATIC_URL", rootProject.ext.apiUrl.testStaticUrl)
            buildConfigField("String", "MINE_MENU_ICON_URL", rootProject.ext.apiUrl.testMineMenuIconUrl)

            buildConfigField("String", "UMAppKey", rootProject.ext.config.UMAppKey_Test)
            buildConfigField("String", "UMMessageSecret", rootProject.ext.config.UMMessageSecret_Test)
            buildConfigField("String", "UMXiaoMiAppID", rootProject.ext.config.UMXiaoMiAppID_Test)
            buildConfigField("String", "UMXiaoMiAppKey", rootProject.ext.config.UMXiaoMiAppKey_Test)

            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationTestId + ".fileprovider" + '\"')

            manifestPlaceholders = [
                    app_name        : "豆芽测试",
                    baidulbs_key    : rootProject.ext.config.BaiduMap_AppKey_Test,
                    file_provider   : rootProject.ext.android.applicationTestId + ".fileprovider",
                    um_hauwei_app_id: rootProject.ext.config.UMHuaWeiAppID_Test
            ]
        }

        betashare {//beta环境可分享
            dimension "default"
            rootProject.ext.android.pkgName = rootProject.ext.android.applicationTestId
            applicationId rootProject.ext.android.applicationId
            buildConfigField("String", "API_URL", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_TEST", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_DEV", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_STAGE", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "H5_URL", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_TEST", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_STAGE", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "H5_DOMAIN", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_TEST", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_STAGE", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "CDN_URL", rootProject.ext.apiUrl.testCDN)
            buildConfigField("String", "STATIC_URL", rootProject.ext.apiUrl.testStaticUrl)
            buildConfigField("String", "MINE_MENU_ICON_URL", rootProject.ext.apiUrl.testMineMenuIconUrl)

            buildConfigField("String", "UMAppKey", rootProject.ext.config.UMAppKey_Test)
            buildConfigField("String", "UMMessageSecret", rootProject.ext.config.UMMessageSecret_Test)
            buildConfigField("String", "UMXiaoMiAppID", rootProject.ext.config.UMXiaoMiAppID_Test)
            buildConfigField("String", "UMXiaoMiAppKey", rootProject.ext.config.UMXiaoMiAppKey_Test)

            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationTestId + ".fileprovider" + '\"')

            manifestPlaceholders = [
                    app_name        : "豆芽测试",
                    baidulbs_key    : rootProject.ext.config.BaiduMap_AppKey_Test,
                    file_provider   : rootProject.ext.android.applicationTestId + ".fileprovider",
                    um_hauwei_app_id: rootProject.ext.config.UMHuaWeiAppID_Test
            ]
        }

        dev {//开发
            dimension "default"
            applicationId rootProject.ext.android.applicationTestId
            buildConfigField("String", "API_URL", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_TEST", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_DEV", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_STAGE", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "H5_URL", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_TEST", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_STAGE", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "CDN_URL", rootProject.ext.apiUrl.devCDN)
            buildConfigField("String", "H5_DOMAIN", rootProject.ext.apiUrl.devH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_TEST", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_STAGE", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "STATIC_URL", rootProject.ext.apiUrl.devStaticUrl)
            buildConfigField("String", "MINE_MENU_ICON_URL", rootProject.ext.apiUrl.devMineMenuIconUrl)

            buildConfigField("String", "UMAppKey", rootProject.ext.config.UMAppKey_Test)
            buildConfigField("String", "UMMessageSecret", rootProject.ext.config.UMMessageSecret_Test)
            buildConfigField("String", "UMXiaoMiAppID", rootProject.ext.config.UMXiaoMiAppID_Test)
            buildConfigField("String", "UMXiaoMiAppKey", rootProject.ext.config.UMXiaoMiAppKey_Test)

            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationTestId + ".fileprovider" + '\"')

            manifestPlaceholders = [
                    app_name        : "豆芽开发",
                    baidulbs_key    : rootProject.ext.config.BaiduMap_AppKey_Test,
                    file_provider   : rootProject.ext.android.applicationTestId + ".fileprovider",
                    um_hauwei_app_id: rootProject.ext.config.UMHuaWeiAppID_Test
            ]
        }
        stage {//预线上
            dimension "default"
            applicationId rootProject.ext.android.applicationTestId
            buildConfigField("String", "API_URL", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "API_URL_TEST", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_DEV", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_STAGE", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "CDN_URL", rootProject.ext.apiUrl.stageCDN)
            buildConfigField("String", "H5_URL", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "H5_URL_TEST", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_STAGE", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "H5_DOMAIN", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_TEST", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_STAGE", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "STATIC_URL", rootProject.ext.apiUrl.stageStaticUrl)
            buildConfigField("String", "MINE_MENU_ICON_URL", rootProject.ext.apiUrl.stageMineMenuIconUrl)


            buildConfigField("String", "UMAppKey", rootProject.ext.config.UMAppKey_Test)
            buildConfigField("String", "UMMessageSecret", rootProject.ext.config.UMMessageSecret_Test)
            buildConfigField("String", "UMXiaoMiAppID", rootProject.ext.config.UMXiaoMiAppID_Test)
            buildConfigField("String", "UMXiaoMiAppKey", rootProject.ext.config.UMXiaoMiAppKey_Test)

            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationTestId + ".fileprovider" + '\"')

            manifestPlaceholders = [
                    app_name        : "豆芽预发",
                    baidulbs_key    : rootProject.ext.config.BaiduMap_AppKey_Test,
                    file_provider   : rootProject.ext.android.applicationTestId + ".fileprovider",
                    um_hauwei_app_id: rootProject.ext.config.UMHuaWeiAppID_Test
            ]
        }
        prod {//线上
            dimension "default"
            applicationId rootProject.ext.android.applicationId
            buildConfigField("String", "API_URL", rootProject.ext.apiUrl.releaseUrl)
            buildConfigField("String", "API_URL_TEST", rootProject.ext.apiUrl.testUrl)
            buildConfigField("String", "API_URL_DEV", rootProject.ext.apiUrl.devUrl)
            buildConfigField("String", "API_URL_STAGE", rootProject.ext.apiUrl.stageUrl)
            buildConfigField("String", "CDN_URL", rootProject.ext.apiUrl.releaseCDN)
            buildConfigField("String", "H5_URL", rootProject.ext.apiUrl.releaseH5Url)
            buildConfigField("String", "H5_URL_TEST", rootProject.ext.apiUrl.testH5Url)
            buildConfigField("String", "H5_URL_STAGE", rootProject.ext.apiUrl.stageH5Url)
            buildConfigField("String", "STATIC_URL", rootProject.ext.apiUrl.releaseStaticUrl)
            buildConfigField("String", "H5_DOMAIN", rootProject.ext.apiUrl.releaseH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_TEST", rootProject.ext.apiUrl.testH5UrlDomain)
            buildConfigField("String", "H5_DOMAIN_STAGE", rootProject.ext.apiUrl.stageH5UrlDomain)
            buildConfigField("String", "MINE_MENU_ICON_URL", rootProject.ext.apiUrl.releaseMineMenuIconUrl)

            buildConfigField("String", "UMAppKey", rootProject.ext.config.UMAppKey)
            buildConfigField("String", "UMMessageSecret", rootProject.ext.config.UMMessageSecret)
            buildConfigField("String", "UMXiaoMiAppID", rootProject.ext.config.UMXiaoMiAppID)
            buildConfigField("String", "UMXiaoMiAppKey", rootProject.ext.config.UMXiaoMiAppKey)

            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationId + ".fileprovider" + '\"')

            manifestPlaceholders = [
                    app_name        : "豆芽",
                    baidulbs_key    : rootProject.ext.config.BaiduMap_AppKey,
                    file_provider   : rootProject.ext.android.applicationId + ".fileprovider",
                    um_hauwei_app_id: rootProject.ext.config.UMHuaWeiAppID
            ]
        }
    }
    packagingOptions {//不知道正式包会不会有影响
        exclude 'META-INF/proguard/androidx-annotations.pro'
    }
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }

}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    //官方support包
    api('androidx.annotation:annotation:1.1.0') {
        force = true
    }
    api('androidx.appcompat:appcompat:1.1.0') {
        force = true
    }
    api 'androidx.legacy:legacy-support-v4:1.0.0'
    api('com.google.android.material:material:1.1.0') {
        force = true
    }
    api 'androidx.recyclerview:recyclerview:1.1.0'
    api 'androidx.constraintlayout:constraintlayout:2.0.4'
    api 'androidx.lifecycle:lifecycle-extensions:2.2.0'


    //流式布局
    implementation 'com.hyman:flowlayout-lib:1.1.2'

    //
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    //注解处理，如果需要处理kotlin代码中的注解，可以换成kapt
    kapt "com.jakewharton:butterknife-compiler:10.2.2"
    kapt 'com.alibaba:arouter-compiler:1.2.2'


    implementation 'com.xyy.crm:ynb:0.0.13'
    implementation 'com.google.code.gson:gson:2.8.6'

    implementation files('libs/Msc.jar')

    // 屏幕适配
    api 'me.jessyan:autosize:1.1.2'

    //渠道版采用的是3.1.0版本，两个版本之间Easing类有api变动，标准版这里直接force版本
    implementation('com.github.PhilJay:MPAndroidChart:v3.1.0') {
        force = true
    }

    implementation project(':XYYContainer')
    implementation project(path: ':flutter')
//    implementation 'com.ybm100.app.crm.flutter:flutter_release:master-7.8.2'

    implementation 'com.huawei.hms:push:5.0.4.302'

    implementation 'com.umeng.umsdk:huawei-umengaccs:1.3.1'

    implementation 'com.umeng.umsdk:xiaomi-push:3.8.5'
    implementation 'com.umeng.umsdk:xiaomi-umengaccs:1.2.1'

//    implementation (project(path: ':platform')) {
//        exclude group:'com.umeng.umsdk', module:'alicloud_beacon'
//        exclude group:'com.umeng.umsdk', module:'alicloud-httpdns'
//        exclude group:'com.umeng.umsdk', module:'utdid'
//    }
    implementation ('com.xyy.crm:platform:2.2.6-standard') {
        exclude group:'com.umeng.umsdk', module:'alicloud_beacon'
        exclude group:'com.umeng.umsdk', module:'alicloud-httpdns'
        exclude group:'com.umeng.umsdk', module:'utdid'
    }

//    implementation project(':appupdate')
    implementation 'com.xyy.canary:appupdate:1.0.5'

    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.6'

    implementation 'com.meituan.android.walle:library:1.1.6'

    implementation 'com.github.barteksc:android-pdf-viewer:2.8.2'

    // Quick Tracking SDK
    implementation 'com.aliyun.ams:alicloud-android-analytics:1.8.0.PX'

}
